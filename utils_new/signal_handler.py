import re
from datetime import datetime
from typing import Optional, Dict, Any
from .api_handler import APIHandler, TradeSignal
from .logger import logger

class SignalHandler:
    def __init__(self):
        self.logger = logger.get_logger('trade')
        self.api = APIHandler()
        
    def _parse_message(self, message: str) -> Optional[TradeSignal]:
        """
        Parse telegram message into trade signal
        Expected format examples:
        - BUY NIFTY 19500 CE
        - SELL BANKNIFTY 45000 PE
        - EXIT NIFTY 19500 CE
        - BUY RELIANCE
        - SELL SBIN
        """
        try:
            # Remove extra whitespace and convert to uppercase
            parts = message.strip().upper().split()
            
            if not parts:
                return None
                
            # Extract action (BUY/SELL/EXIT)
            action = parts[0]
            if action not in ['BUY', 'SELL', 'EXIT']:
                return None
                
            # Extract symbol
            symbol = parts[1]
            
            # Check if it's an options trade
            if len(parts) == 4:
                strike = float(parts[2])
                option_type = parts[3]
                if option_type not in ['CE', 'PE']:
                    return None
                    
                return TradeSignal(
                    symbol=symbol,
                    action=action,
                    strike=strike,
                    option_type=option_type,
                    timestamp=datetime.now()
                )
            elif len(parts) == 2:
                # Stock/Index trade
                return TradeSignal(
                    symbol=symbol,
                    action=action,
                    timestamp=datetime.now()
                )
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error parsing message: {e}")
            return None

    async def handle_telegram_message(self, message: str) -> Dict[str, Any]:
        """Process incoming telegram message"""
        try:
            # Log incoming message
            self.logger.info(f"Processing message: {message}")
            
            # Parse the message
            signal = self._parse_message(message)
            if not signal:
                return {
                    "status": "error",
                    "message": "Invalid signal format. Expected format: 'ACTION SYMBOL [STRIKE] [CE/PE]'"
                }
            
            # Process the signal
            result = self.api.process_signal(signal)
            
            # Log the result
            if result["status"] == "success":
                self.logger.info(f"Signal processed successfully: {result}")
            else:
                self.logger.error(f"Signal processing failed: {result}")
                
            return result
            
        except Exception as e:
            error_msg = f"Message handling failed: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}
