[E 250909 10:55:52 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/historical/v1/getCandleData. Error: Something Went Wrong, Please Try After Sometime. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/historical/v1/getCandleData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'e3:0d:88:b7:c8:40', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NFO', 'symboltoken': '44637', 'interval': 'ONE_MINUTE', 'fromdate': '2025-09-08 09:15', 'todate': '2025-09-09 15:30'}, Response: {'message': 'Something Went Wrong, Please Try After Sometime', 'errorcode': 'AB1004', 'status': False, 'data': None}
[E 250909 11:00:40 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/historical/v1/getCandleData. Error: Something Went Wrong, Please Try After Sometime. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/historical/v1/getCandleData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'e3:0d:88:b7:c8:40', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NFO', 'symboltoken': '44640', 'interval': 'ONE_MINUTE', 'fromdate': '2025-09-08 09:15', 'todate': '2025-09-09 15:30'}, Response: {'message': 'Something Went Wrong, Please Try After Sometime', 'errorcode': 'AB1004', 'status': False, 'data': None}
[E 250909 12:00:48 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/historical/v1/getCandleData. Error: Something Went Wrong, Please Try After Sometime. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/historical/v1/getCandleData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'e3:0d:88:b7:c8:40', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'BFO', 'symboltoken': '859054', 'interval': 'ONE_MINUTE', 'fromdate': '2025-09-08 09:15', 'todate': '2025-09-09 15:30'}, Response: {'message': 'Something Went Wrong, Please Try After Sometime', 'errorcode': 'AB1004', 'status': False, 'data': None}
