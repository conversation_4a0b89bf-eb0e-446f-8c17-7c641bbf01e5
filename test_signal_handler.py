#!/usr/bin/env python3
"""
Test script to verify signal handler can parse all the given formats
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils_new.signal_handler import SignalHandler
import asyncio

def test_signal_parsing():
    """Test all signal formats"""
    handler = SignalHandler()
    
    # Test cases from the user's examples
    test_messages = [
        # Format 1: TRADE BUY/SELL signals
        "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$152.9$146.3$2025-09-12 14:33:00+05:30",
        "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$30.6$28.6$2025-09-12 14:48:00+05:30",
        
        # Format 2: INTIMATION signals
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 PUT at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 30.05 (Entry price: 30.6) (Stop loss: 28.6)",
        
        # Format 3: TRADE CLOSE signals
        "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 PUT$157.4$2025-09-12 14:35:00+05:30$stop_loss_exit$157.4",
        "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 PUT$30.05$2025-09-12 15:00:00+05:30$stop_loss_exit$30.33",
        
        # Format 4: UPDATE signals
        "===Algo_Trading===$Update$STOP LOSS to$154.08$for option$SENSEX 18 SEP 81500 PUT current price: 157.8 (Entry price: 155.3)",
        "===Algo_Trading===$Update$STOP LOSS to$30.01$for option$NIFTY 16 SEP 25000 PUT",
        "===Algo_Trading===$Update$STOP LOSS to$30.13$for option$NIFTY 16 SEP 25000 PUT current price: 31.05 (Entry price: 30.6)",
        
        # Format 5: CLOSE with different exit reason
        "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24700 PUT$82.35$2025-09-08 15:15:00+05:30$opposite_crossover_exit",
        
        # Simple formats
        "BUY NIFTY 19500 CE",
        "SELL BANKNIFTY 45000 PE",
        "EXIT NIFTY 19500 CE"
    ]
    
    print("Testing Signal Handler Parsing...")
    print("=" * 80)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\nTest {i}: {message[:60]}...")
        
        try:
            signal = handler._parse_message(message)
            if signal:
                print(f"✅ PARSED SUCCESSFULLY")
                print(f"   Symbol: {signal.symbol}")
                print(f"   Action: {signal.action}")
                print(f"   Signal Type: {signal.signal_type}")
                print(f"   Strike: {signal.strike}")
                print(f"   Option Type: {signal.option_type}")
                print(f"   Expiry Date: {signal.expiry_date}")
                print(f"   Price: {signal.price}")
                print(f"   Entry Price: {signal.entry_price}")
                print(f"   Stop Loss: {signal.stop_loss}")
                print(f"   Current Price: {signal.current_price}")
                print(f"   Exit Price: {signal.exit_price}")
                print(f"   Exit Reason: {signal.exit_reason}")
                print(f"   Update Type: {signal.update_type}")
                print(f"   New Stop Loss: {signal.new_stop_loss}")
            else:
                print(f"❌ PARSING FAILED")
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    print("\n" + "=" * 80)
    print("Testing complete!")

async def test_message_handling():
    """Test the full message handling pipeline"""
    handler = SignalHandler()
    
    test_messages = [
        "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$152.9$146.3$2025-09-12 14:33:00+05:30",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 PUT at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)",
        "===Algo_Trading===$Update$STOP LOSS to$154.08$for option$SENSEX 18 SEP 81500 PUT current price: 157.8 (Entry price: 155.3)",
        "BUY NIFTY 19500 CE"
    ]
    
    print("\nTesting Message Handling...")
    print("=" * 80)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\nHandling Test {i}: {message[:60]}...")
        
        try:
            result = await handler.handle_telegram_message(message)
            print(f"Result: {result}")
        except Exception as e:
            print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    # Test parsing only (doesn't require API connections)
    test_signal_parsing()
    
    # Uncomment to test full message handling (requires API setup)
    # asyncio.run(test_message_handling())
